---
import Layout from "@/layouts/Layout.astro";
import { getCollection } from "astro:content";
import { getRelatedPosts, formatBlogDate } from "@/services/blog-server";
import { BlogCard } from "@/components/blog/BlogCard.tsx";

// Export getStaticPaths for static generation
export async function getStaticPaths() {
  const blogEntries = await getCollection("blog");
  return blogEntries.map((entry) => ({
    params: { slug: entry.slug },
  }));
}

// Get the slug from the URL params
const { slug } = Astro.params;

// Get the blog entry dynamically
const blogEntries = await getCollection("blog");
const entry = blogEntries.find((e) => e.slug === slug);

// If entry not found, return 404
if (!entry) {
  return Astro.redirect("/404");
}
const { Content } = await entry.render();

// Transform entry to post format for compatibility
const post = {
  id: entry.slug,
  title: entry.data.title,
  slug: entry.slug,
  excerpt: entry.data.excerpt,
  content: entry.body,
  category: entry.data.category,
  tags: entry.data.tags,
  publishDate: entry.data.publishDate.toISOString(),
  lastUpdated:
    entry.data.lastUpdated?.toISOString() ||
    entry.data.publishDate.toISOString(),
  author: {
    name: entry.data.author.name,
    role: entry.data.author.role,
  },
  readingTime: entry.data.readingTime || 5,
  featured: entry.data.featured,
  relatedModelIds: entry.data.relatedModelIds || [],
  relatedBenchmarks: entry.data.relatedBenchmarks || [],
  releaseVersion: entry.data.releaseVersion,
  changelog: entry.data.changelog || [],
  metaDescription: entry.data.metaDescription,
  metaKeywords: entry.data.metaKeywords || [],
  featuredImage: entry.data.featuredImage,
  gallery: entry.data.gallery || [],
} as any; // Type assertion to avoid compatibility issues

const relatedPosts = await getRelatedPosts(post, 3);

if (!entry) {
  return Astro.redirect("/blog/");
}

const pageTitle = `${post.title} - LLM Browser Blog`;
const pageDescription = post.metaDescription || post.excerpt;
---

<Layout title={pageTitle} description={pageDescription}>
  <main class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Back Navigation -->
    <div class="mb-8">
      <a
        href="/blog/"
        class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"></path>
        </svg>
        Zurück zum Blog
      </a>
    </div>

    <!-- Article Header -->
    <article class="w-full">
      <header class="mb-12">
        <!-- Category Badge -->
        <div class="mb-6">
          {
            post.category === "model-analysis" && (
              <span class="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium border border-blue-200">
                Modell-Analyse
              </span>
            )
          }
          {
            post.category === "release-notes" && (
              <span class="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium border border-green-200">
                Release Notes
              </span>
            )
          }
          {
            post.category === "benchmark-analysis" && (
              <span class="inline-block px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium border border-purple-200">
                Benchmark-Analyse
              </span>
            )
          }
          {
            post.category === "industry-news" && (
              <span class="inline-block px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium border border-orange-200">
                Branchen-News
              </span>
            )
          }
          {
            post.featured && (
              <span class="inline-block px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium border border-yellow-200 ml-2">
                Featured
              </span>
            )
          }
        </div>

        <!-- Title -->
        <h1
          class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight"
        >
          {post.title}
        </h1>

        <!-- Excerpt -->
        <p class="text-xl text-gray-600 mb-8 leading-relaxed max-w-3xl">
          {post.excerpt}
        </p>

        <!-- Meta Information -->
        <div
          class="flex flex-wrap items-center gap-6 text-sm text-gray-500 mb-8 pb-6 border-b border-gray-200"
        >
          <div class="flex items-center gap-2">
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              ></path>
            </svg>
            <span>{formatBlogDate(post.publishDate)}</span>
          </div>
          <div class="flex items-center gap-2">
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>{post.readingTime} Min. Lesezeit</span>
          </div>
          <div class="flex items-center gap-2">
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              ></path>
            </svg>
            <span>{post.author.name} - {post.author.role}</span>
          </div>
        </div>

        <!-- Tags -->
        {
          post.tags.length > 0 && (
            <div class="flex items-start gap-2 mb-8">
              <svg
                class="w-4 h-4 mt-1 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                />
              </svg>
              <div class="flex flex-wrap gap-2">
                {post.tags.map((tag: string) => (
                  <span class="inline-block px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )
        }

        <!-- Featured Image -->
        <div class="mb-12">
          <img
            src={post.featuredImage || "/images/blog/blog-default.png"}
            alt={post.title}
            class="w-full h-80 md:h-96 object-cover rounded-xl shadow-xl"
            onerror="this.src='/images/blog/blog-default.png'"
          />
        </div>
      </header>

      <!-- Article Content -->
      <div class="prose prose-lg max-w-none mb-16">
        <Content />
      </div>

      <!-- Release Notes Changelog (if applicable) -->
      {
        post.category === "release-notes" && post.changelog && (
          <div class="bg-white border rounded-lg shadow-sm mb-12">
            <div class="p-6">
              <h2 class="text-xl font-semibold mb-4">
                Changelog v{post.releaseVersion}
              </h2>
              <div class="space-y-4">
                {post.changelog.map((entry: any) => (
                  <div
                    class={`border-l-4 pl-4 ${
                      entry.type === "added"
                        ? "border-green-400 bg-green-50"
                        : entry.type === "changed"
                          ? "border-blue-400 bg-blue-50"
                          : entry.type === "deprecated"
                            ? "border-yellow-400 bg-yellow-50"
                            : entry.type === "removed"
                              ? "border-red-400 bg-red-50"
                              : entry.type === "fixed"
                                ? "border-purple-400 bg-purple-50"
                                : "border-orange-400 bg-orange-50"
                    }`}
                  >
                    <div class="flex items-center gap-2 mb-1">
                      <span
                        class={`text-xs font-semibold uppercase tracking-wide ${
                          entry.type === "added"
                            ? "text-green-700"
                            : entry.type === "changed"
                              ? "text-blue-700"
                              : entry.type === "deprecated"
                                ? "text-yellow-700"
                                : entry.type === "removed"
                                  ? "text-red-700"
                                  : entry.type === "fixed"
                                    ? "text-purple-700"
                                    : "text-orange-700"
                        }`}
                      >
                        {entry.type}
                      </span>
                      <span
                        class={`text-xs px-2 py-1 rounded-full ${
                          entry.impact === "major"
                            ? "bg-red-100 text-red-800"
                            : entry.impact === "minor"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-green-100 text-green-800"
                        }`}
                      >
                        {entry.impact}
                      </span>
                    </div>
                    <p class="text-sm text-gray-700 mb-1">
                      {entry.description}
                    </p>
                    {entry.technicalDetails && (
                      <p class="text-xs text-gray-600 italic">
                        {entry.technicalDetails}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )
      }

      <!-- Related Models/Benchmarks Info -->
      {
        (post.relatedModelIds || post.relatedBenchmarks) && (
          <div class="bg-white border rounded-lg shadow-sm mb-12">
            <div class="p-6">
              <h2 class="text-xl font-semibold mb-4">
                Verwandte Informationen
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                {post.relatedModelIds && post.relatedModelIds.length > 0 && (
                  <div>
                    <h3 class="font-medium text-gray-900 mb-2">
                      Verwandte Modelle
                    </h3>
                    <div class="space-y-1">
                      {post.relatedModelIds.map((modelId: string) => (
                        <a
                          href={`/models/?search=${encodeURIComponent(modelId)}`}
                          class="block text-sm text-blue-600 hover:text-blue-700 hover:underline"
                        >
                          {modelId}
                        </a>
                      ))}
                    </div>
                  </div>
                )}
                {post.relatedBenchmarks &&
                  post.relatedBenchmarks.length > 0 && (
                    <div>
                      <h3 class="font-medium text-gray-900 mb-2">
                        Verwandte Benchmarks
                      </h3>
                      <div class="space-y-1">
                        {post.relatedBenchmarks.map((benchmark: string) => (
                          <a
                            href={`/benchmark/?search=${encodeURIComponent(benchmark)}`}
                            class="block text-sm text-blue-600 hover:text-blue-700 hover:underline"
                          >
                            {benchmark}
                          </a>
                        ))}
                      </div>
                    </div>
                  )}
              </div>
            </div>
          </div>
        )
      }
    </article>

    <!-- Related Posts -->
    {
      relatedPosts.length > 0 && (
        <section class="max-w-6xl mx-auto mt-16">
          <h2 class="text-2xl font-semibold text-gray-900 mb-6">
            Ähnliche Artikel
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedPosts.map((relatedPost) => (
              <BlogCard post={relatedPost} client:load />
            ))}
          </div>
        </section>
      )
    }
  </main>
</Layout>

<style>
  /* Article-specific styles */
  .prose {
    color: #374151;
    line-height: 1.7;
    font-size: 1rem;
    max-width: none;
  }

  /* Main headings (##) - Ensure they are visible */
  .prose :global(h2) {
    font-size: 1.875rem !important;
    font-weight: 700 !important;
    color: #111827 !important;
    margin-top: 3rem !important;
    margin-bottom: 1.5rem !important;
    padding-bottom: 0.75rem !important;
    border-bottom: 2px solid #dbeafe !important;
    display: block !important;
    visibility: visible !important;
  }

  .prose :global(h2:first-of-type) {
    margin-top: 1.5rem !important;
  }

  /* Sub-headings (###) - Ensure they are visible */
  .prose :global(h3) {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin-top: 2.5rem !important;
    margin-bottom: 1.25rem !important;
    padding: 0.75rem 0 !important;
    display: block !important;
    visibility: visible !important;
  }

  /* Sub-sub-headings (####) - Ensure they are visible */
  .prose :global(h4) {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin-top: 2rem !important;
    margin-bottom: 1rem !important;
    padding: 0.5rem 0 !important;
    display: block !important;
    visibility: visible !important;
  }

  /* Paragraph styling */
  .prose :global(p) {
    margin-bottom: 1.25rem !important;
    line-height: 1.7 !important;
    display: block !important;
    visibility: visible !important;
  }

  /* List styling */
  .prose :global(ul),
  .prose :global(ol) {
    margin-bottom: 1.5rem !important;
    margin-top: 1rem !important;
    padding-left: 2rem !important;
    display: block !important;
    visibility: visible !important;
    list-style-position: outside !important;
  }

  .prose :global(ul) {
    list-style-type: disc !important;
  }

  .prose :global(ol) {
    list-style-type: decimal !important;
  }

  .prose :global(li) {
    margin-bottom: 0.75rem !important;
    line-height: 1.6 !important;
    display: list-item !important;
    visibility: visible !important;
    padding-left: 0.5rem !important;
  }

  .prose :global(ul li) {
    position: relative;
  }

  /* Nested lists */
  .prose :global(ul ul),
  .prose :global(ol ol),
  .prose :global(ul ol),
  .prose :global(ol ul) {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    padding-left: 1.5rem !important;
  }

  .prose :global(ul ul) {
    list-style-type: circle !important;
  }

  .prose :global(ul ul ul) {
    list-style-type: square !important;
  }

  /* Strong text styling */
  .prose :global(strong) {
    font-weight: 600 !important;
    color: #111827 !important;
  }

  .prose :global(li strong) {
    color: #111827 !important;
  }

  /* Link styling */
  .prose :global(a) {
    color: #2563eb !important;
    text-decoration: underline !important;
    text-decoration-color: #93c5fd !important;
    transition: all 0.2s ease !important;
  }

  .prose :global(a:hover) {
    color: #1d4ed8 !important;
    text-decoration-color: #2563eb !important;
  }

  /* Code styling */
  .prose :global(code) {
    background-color: #f3f4f6 !important;
    padding: 0.125rem 0.25rem !important;
    border-radius: 0.25rem !important;
    font-size: 0.875rem !important;
    font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
      "Courier New", monospace !important;
  }

  .prose :global(pre) {
    background-color: #f8f9fa !important;
    color: #374151 !important;
    padding: 1.5rem !important;
    border-radius: 0.5rem !important;
    border: 1px solid #e5e7eb !important;
    overflow-x: auto !important;
    margin: 1.5rem 0 !important;
    font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
      "Courier New", monospace !important;
    line-height: 1.6 !important;
  }

  /* Blockquote styling */
  .prose blockquote {
    border-left: 4px solid theme("colors.blue.400");
    padding-left: 1rem;
    font-style: italic;
    color: theme("colors.gray.600");
    margin: 1.5rem 0;
  }

  /* Horizontal rule */
  .prose hr {
    border: none;
    border-top: 1px solid theme("colors.gray.300");
    margin: 2.5rem 0;
  }

  /* Emphasis styling */
  .prose em {
    font-style: italic;
    color: theme("colors.gray.600");
  }

  /* Better spacing for nested lists */
  .prose li ul,
  .prose li ol {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  /* Special styling for sections with emojis */
  .prose h3:first-letter {
    font-size: 1.2em;
  }

  /* Table styling with enhanced readability */
  .prose :global(table) {
    width: 100% !important;
    border-collapse: collapse !important;
    margin: 2rem 0 !important;
    background-color: #ffffff !important;
    border-radius: 0.5rem !important;
    overflow: hidden !important;
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    border: 1px solid #e5e7eb !important;
    display: table !important;
    visibility: visible !important;
  }

  .prose :global(thead) {
    background-color: #f8fafc !important;
    border-bottom: 2px solid #e2e8f0 !important;
  }

  .prose :global(th) {
    padding: 1rem 1.25rem !important;
    text-align: left !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    color: #374151 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
    border-bottom: 1px solid #d1d5db !important;
    background-color: #f8fafc !important;
  }

  .prose :global(td) {
    padding: 1rem 1.25rem !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    color: #374151 !important;
    border-bottom: 1px solid #e5e7eb !important;
    vertical-align: top !important;
  }

  /* Zebra striping for better readability */
  .prose :global(tbody tr:nth-child(even)) {
    background-color: #f9fafb !important;
  }

  .prose :global(tbody tr:nth-child(odd)) {
    background-color: #ffffff !important;
  }

  /* Hover effect for table rows */
  .prose :global(tbody tr:hover) {
    background-color: #f3f4f6 !important;
    transition: background-color 0.2s ease !important;
  }

  /* Strong text in table cells */
  .prose :global(td strong),
  .prose :global(th strong) {
    font-weight: 700 !important;
    color: #111827 !important;
  }

  /* Links in table cells */
  .prose :global(td a) {
    color: #2563eb !important;
    text-decoration: none !important;
    font-weight: 500 !important;
  }

  .prose :global(td a:hover) {
    color: #1d4ed8 !important;
    text-decoration: underline !important;
  }

  /* Code in table cells */
  .prose :global(td code),
  .prose :global(th code) {
    background-color: #f1f5f9 !important;
    padding: 0.125rem 0.375rem !important;
    border-radius: 0.25rem !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
  }

  /* Responsive table design */
  @media (max-width: 768px) {
    .prose :global(table) {
      font-size: 0.75rem !important;
    }

    .prose :global(th),
    .prose :global(td) {
      padding: 0.75rem 0.5rem !important;
    }

    .prose :global(th) {
      font-size: 0.75rem !important;
    }
  }

  /* Special styling for model comparison tables */
  .prose :global(table) :global(td:first-child) {
    font-weight: 600 !important;
    background-color: #f8fafc !important;
    border-right: 1px solid #e2e8f0 !important;
  }

  /* Highlight cells with performance metrics */
  .prose :global(td):has-text("93.4%"),
  .prose :global(td):has-text("91.6%"),
  .prose :global(td):has-text("72.7%"),
  .prose :global(td):has-text("76.3%") {
    background-color: #dcfce7 !important;
    color: #166534 !important;
    font-weight: 600 !important;
  }
</style>
